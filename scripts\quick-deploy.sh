#!/bin/bash

# 快速部署脚本 - 适用于已有代码的情况
set -e

# 配置变量
PROJECT_DIR="/www/wwwroot/burner-repair"
DATE=$(date +%Y%m%d_%H%M%S)

echo "🚀 燃烧器维修系统 - 快速部署脚本"
echo "=================================="

# 检查当前目录是否包含项目文件
if [ ! -f "docker-compose.prod.yml" ] || [ ! -d "api" ] || [ ! -d "web" ]; then
    echo "❌ 错误：请在项目根目录执行此脚本"
    echo "确保当前目录包含以下文件/目录："
    echo "  - docker-compose.prod.yml"
    echo "  - api/"
    echo "  - web/"
    exit 1
fi

echo "✅ 项目文件检查通过"

# 创建目标目录
echo "📁 创建部署目录..."
sudo mkdir -p $PROJECT_DIR
sudo mkdir -p $PROJECT_DIR/{data/mongodb,data/uploads,logs/{api,web,mongodb},config}

# 复制项目文件
echo "📋 复制项目文件..."
sudo cp -r . $PROJECT_DIR/
sudo cp docker-compose.prod.yml $PROJECT_DIR/docker-compose.yml

# 设置权限
echo "🔐 设置文件权限..."
sudo chown -R www:www $PROJECT_DIR
sudo chmod -R 755 $PROJECT_DIR
sudo chmod +x $PROJECT_DIR/scripts/*.sh

# 创建环境配置文件
echo "⚙️ 创建环境配置..."

# API环境配置
sudo tee $PROJECT_DIR/api/.env.production > /dev/null << 'EOF'
NODE_ENV=production
PORT=12140
MONGODB_URI=mongodb://admin:BurnerRepair2024!@#@mongodb:27017/burner_repair?authSource=admin
JWT_SECRET=BurnerRepairJWT2024SecretKey!@#$%^&*
JWT_REFRESH_SECRET=BurnerRepairRefreshJWT2024SecretKey!@#$%^&*
UPLOAD_PATH=/app/uploads
LOG_LEVEL=info
CORS_ORIGIN=https://br.smithyan.xyz
EOF

# Web环境配置
sudo tee $PROJECT_DIR/web/.env.production > /dev/null << 'EOF'
NODE_ENV=production
VITE_API_BASE_URL=https://brapi.smithyan.xyz
VITE_WS_URL=wss://brapi.smithyan.xyz
EOF

# 创建MongoDB配置
echo "🗄️ 创建MongoDB配置..."
sudo tee $PROJECT_DIR/config/mongod.conf > /dev/null << 'EOF'
storage:
  dbPath: /data/db
  journal:
    enabled: true

systemLog:
  destination: file
  logAppend: true
  path: /var/log/mongodb/mongod.log
  logRotate: reopen

net:
  port: 27017
  bindIp: 0.0.0.0

processManagement:
  timeZoneInfo: /usr/share/zoneinfo

security:
  authorization: enabled

setParameter:
  authenticationMechanisms: SCRAM-SHA-1,SCRAM-SHA-256
EOF

# 进入项目目录
cd $PROJECT_DIR

# 停止现有服务
echo "⏹️ 停止现有服务..."
sudo docker-compose down --remove-orphans 2>/dev/null || true

# 拉取镜像并启动服务
echo "🔨 启动Docker服务..."
sudo docker-compose pull
sudo docker-compose up -d --build

# 等待服务启动
echo "⏳ 等待服务启动（60秒）..."
sleep 60

# 健康检查
echo "🔍 执行健康检查..."

# 检查容器状态
echo "📊 Docker容器状态："
sudo docker-compose ps

# 检查API健康
echo "🔍 检查API服务..."
if curl -f -s http://localhost:12140/api/v1/health > /dev/null; then
    echo "✅ API服务正常"
else
    echo "⚠️ API服务可能还在启动中，请稍后检查"
fi

# 检查Web服务
echo "🔍 检查Web服务..."
if curl -f -s http://localhost:12141 > /dev/null; then
    echo "✅ Web服务正常"
else
    echo "⚠️ Web服务可能还在启动中，请稍后检查"
fi

# 显示服务信息
echo ""
echo "🎉 部署完成！"
echo "=================================="
echo "📊 服务信息："
echo "  - MongoDB: localhost:12139"
echo "  - API服务: localhost:12140"
echo "  - Web服务: localhost:12141"
echo ""
echo "🌐 访问地址（需要配置Nginx反向代理）："
echo "  - API: https://brapi.smithyan.xyz"
echo "  - Web: https://br.smithyan.xyz"
echo ""
echo "📋 下一步操作："
echo "1. 在宝塔面板中添加域名站点"
echo "2. 申请SSL证书"
echo "3. 配置Nginx反向代理（参考nginx/目录下的配置文件）"
echo "4. 设置定时任务执行监控脚本"
echo ""
echo "📁 重要目录："
echo "  - 项目目录: $PROJECT_DIR"
echo "  - 数据目录: $PROJECT_DIR/data"
echo "  - 日志目录: $PROJECT_DIR/logs"
echo "  - 配置目录: $PROJECT_DIR/config"
echo ""
echo "🔧 管理命令："
echo "  - 查看状态: cd $PROJECT_DIR && docker-compose ps"
echo "  - 查看日志: cd $PROJECT_DIR && docker-compose logs -f"
echo "  - 重启服务: cd $PROJECT_DIR && docker-compose restart"
echo "  - 停止服务: cd $PROJECT_DIR && docker-compose down"
echo ""
echo "📞 如有问题，请检查日志文件："
echo "  - API日志: $PROJECT_DIR/logs/api/"
echo "  - Web日志: $PROJECT_DIR/logs/web/"
echo "  - MongoDB日志: $PROJECT_DIR/logs/mongodb/"
