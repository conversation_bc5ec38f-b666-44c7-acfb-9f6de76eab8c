version: '3.8'

services:
  mongodb:
    image: mongo:7.0
    container_name: burner-repair-mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: BurnerRepair2024!@#
      MONGO_INITDB_DATABASE: burner_repair
    ports:
      - "12139:27017"
    volumes:
      - ./data/mongodb:/data/db
      - ./logs/mongodb:/var/log/mongodb
      - ./config/mongod.conf:/etc/mongod.conf:ro
    networks:
      - burner-repair-network
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  api:
    image: node:18-alpine
    container_name: burner-repair-api
    restart: unless-stopped
    working_dir: /app
    environment:
      NODE_ENV: production
      PORT: 12140
      MONGODB_URI: mongodb://admin:BurnerRepair2024!@#@mongodb:27017/burner_repair?authSource=admin
      JWT_SECRET: BurnerRepairJWT2024SecretKey!@#$%^&*
      JWT_REFRESH_SECRET: BurnerRepairRefreshJWT2024SecretKey!@#$%^&*
      UPLOAD_PATH: /app/uploads
      LOG_LEVEL: info
      CORS_ORIGIN: https://br.smithyan.xyz
    ports:
      - "12140:12140"
    volumes:
      - ./api:/app
      - ./data/uploads:/app/uploads
      - ./logs/api:/app/logs
    networks:
      - burner-repair-network
    depends_on:
      mongodb:
        condition: service_healthy
    command: >
      sh -c "
        npm install --production &&
        npm run build &&
        npm start
      "
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:12140/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  web:
    image: node:18-alpine
    container_name: burner-repair-web
    restart: unless-stopped
    working_dir: /app
    environment:
      NODE_ENV: production
      VITE_API_BASE_URL: https://brapi.smithyan.xyz
      VITE_WS_URL: wss://brapi.smithyan.xyz
    ports:
      - "12141:12141"
    volumes:
      - ./web:/app
      - ./logs/web:/app/logs
    networks:
      - burner-repair-network
    command: >
      sh -c "
        npm install &&
        npm run build &&
        npm run preview -- --host 0.0.0.0 --port 12141
      "
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:12141"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

networks:
  burner-repair-network:
    driver: bridge

volumes:
  mongodb_data:
    driver: local
  uploads_data:
    driver: local
