// 测试前端用户管理功能的脚本
// 这个脚本会检查前端用户管理页面是否能正确处理用户数据

const http = require('http');

function testFrontendConnection() {
  console.log('正在测试前端服务器连接...');

  return new Promise((resolve, reject) => {
    const req = http.get('http://localhost:12141', (res) => {
      console.log('✓ 前端服务器响应正常');
      console.log(`状态码: ${res.statusCode}`);
      console.log(`Content-Type: ${res.headers['content-type']}`);

      let data = '';
      res.on('data', chunk => {
        data += chunk;
      });

      res.on('end', () => {
        // 检查HTML内容中是否包含用户管理相关的内容
        const hasUserManagement = data.includes('用户管理') || data.includes('Users') || data.includes('user');
        const hasRoles = data.includes('系统管理员') || data.includes('维修管理员') || data.includes('技术员') || data.includes('客户');
        const hasReact = data.includes('react') || data.includes('React');

        console.log('\n页面内容分析:');
        console.log(`✓ 包含用户管理相关内容: ${hasUserManagement ? '是' : '否'}`);
        console.log(`✓ 包含角色相关内容: ${hasRoles ? '是' : '否'}`);
        console.log(`✓ 包含React相关内容: ${hasReact ? '是' : '否'}`);

        if (data.length > 0) {
          console.log(`✓ 页面内容长度: ${data.length} 字符`);
        }

        resolve();
      });
    });

    req.on('error', (error) => {
      console.error('✗ 前端服务器连接失败:', error.message);
      reject(error);
    });

    req.setTimeout(5000, () => {
      console.error('✗ 前端服务器连接超时');
      req.destroy();
      reject(new Error('连接超时'));
    });
  });
}

// 运行测试
async function runTests() {
  try {
    await testFrontendConnection();
    console.log('\n✓ 前端连接测试完成');
    console.log('\n建议手动访问 http://localhost:12141 来测试用户管理功能');
    console.log('检查以下功能是否正常:');
    console.log('1. 用户列表显示');
    console.log('2. 用户角色显示（系统管理员、维修管理员、技术员、客户）');
    console.log('3. 用户创建/编辑表单');
    console.log('4. 用户状态切换');
    console.log('5. 用户删除功能');
  } catch (error) {
    console.error('测试失败:', error.message);
  }
}

runTests();
