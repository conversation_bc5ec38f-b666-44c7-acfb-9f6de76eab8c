# 燃烧器维修系统 - 生产环境部署指南

## 📋 部署概览

### 服务器信息
- **操作系统**: 宝塔Linux面板阿里云专享版 9.2.0
- **已装软件**: 宝塔面板、Nginx、Docker
- **服务器配置**: 内存1.8Gi、Swap1.0Gi、磁盘40GiB

### 服务部署架构
```
Internet
    ↓
Nginx (SSL终端 + 反向代理)
    ↓
┌─────────────────┬─────────────────┬─────────────────┐
│   MongoDB       │   Backend API   │   Frontend Web  │
│   Port: 12139   │   Port: 12140   │   Port: 12141   │
│   (Docker)      │   (Docker)      │   (Docker)      │
└─────────────────┴─────────────────┴─────────────────┘
```

### 域名配置
- **API域名**: brapi.smithyan.xyz → 12140
- **Web域名**: br.smithyan.xyz → 12141
- **SSL证书**: Let's Encrypt (宝塔面板自动申请)

## 🚀 部署步骤

### 第一步：准备部署目录结构

```bash
# 创建项目根目录
mkdir -p /www/wwwroot/burner-repair
cd /www/wwwroot/burner-repair

# 创建目录结构
mkdir -p {data/mongodb,data/uploads,logs/api,logs/web,logs/mongodb,config,ssl,scripts}

# 设置权限
chmod -R 755 /www/wwwroot/burner-repair
chown -R www:www /www/wwwroot/burner-repair
```

### 第二步：创建Docker Compose配置

创建 `docker-compose.yml`:

```yaml
version: '3.8'

services:
  mongodb:
    image: mongo:7.0
    container_name: burner-repair-mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: BurnerRepair2024!@#
      MONGO_INITDB_DATABASE: burner_repair
    ports:
      - "12139:27017"
    volumes:
      - ./data/mongodb:/data/db
      - ./logs/mongodb:/var/log/mongodb
      - ./config/mongod.conf:/etc/mongod.conf:ro
    networks:
      - burner-repair-network
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  api:
    image: node:18-alpine
    container_name: burner-repair-api
    restart: unless-stopped
    working_dir: /app
    environment:
      NODE_ENV: production
      PORT: 12140
      MONGODB_URI: mongodb://admin:BurnerRepair2024!@#@mongodb:27017/burner_repair?authSource=admin
      JWT_SECRET: BurnerRepairJWT2024SecretKey!@#$%^&*
      JWT_REFRESH_SECRET: BurnerRepairRefreshJWT2024SecretKey!@#$%^&*
      UPLOAD_PATH: /app/uploads
      LOG_LEVEL: info
    ports:
      - "12140:12140"
    volumes:
      - ./api:/app
      - ./data/uploads:/app/uploads
      - ./logs/api:/app/logs
    networks:
      - burner-repair-network
    depends_on:
      mongodb:
        condition: service_healthy
    command: >
      sh -c "
        npm install --production &&
        npm run build &&
        npm start
      "
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:12140/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  web:
    image: node:18-alpine
    container_name: burner-repair-web
    restart: unless-stopped
    working_dir: /app
    environment:
      NODE_ENV: production
      VITE_API_BASE_URL: https://brapi.smithyan.xyz
      VITE_WS_URL: wss://brapi.smithyan.xyz
    ports:
      - "12141:12141"
    volumes:
      - ./web:/app
      - ./logs/web:/app/logs
    networks:
      - burner-repair-network
    command: >
      sh -c "
        npm install &&
        npm run build &&
        npm run preview -- --host 0.0.0.0 --port 12141
      "
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:12141"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

networks:
  burner-repair-network:
    driver: bridge

volumes:
  mongodb_data:
    driver: local
  uploads_data:
    driver: local
```

### 第三步：创建MongoDB配置文件

创建 `config/mongod.conf`:

```yaml
# MongoDB配置文件
storage:
  dbPath: /data/db
  journal:
    enabled: true

systemLog:
  destination: file
  logAppend: true
  path: /var/log/mongodb/mongod.log
  logRotate: reopen

net:
  port: 27017
  bindIp: 0.0.0.0

processManagement:
  timeZoneInfo: /usr/share/zoneinfo

security:
  authorization: enabled

setParameter:
  authenticationMechanisms: SCRAM-SHA-1,SCRAM-SHA-256
```

### 第四步：创建部署脚本

创建 `scripts/deploy.sh`:

```bash
#!/bin/bash

# 燃烧器维修系统部署脚本
set -e

PROJECT_DIR="/www/wwwroot/burner-repair"
BACKUP_DIR="/www/backup/burner-repair"
DATE=$(date +%Y%m%d_%H%M%S)

echo "🚀 开始部署燃烧器维修系统..."

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份当前数据（如果存在）
if [ -d "$PROJECT_DIR/data" ]; then
    echo "📦 备份当前数据..."
    tar -czf "$BACKUP_DIR/data_backup_$DATE.tar.gz" -C "$PROJECT_DIR" data
fi

# 停止现有服务
echo "⏹️ 停止现有服务..."
cd $PROJECT_DIR
docker-compose down --remove-orphans || true

# 拉取最新镜像
echo "📥 拉取最新镜像..."
docker-compose pull

# 构建并启动服务
echo "🔨 构建并启动服务..."
docker-compose up -d --build

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 30

# 健康检查
echo "🔍 执行健康检查..."
docker-compose ps

# 检查服务状态
if docker-compose ps | grep -q "Up"; then
    echo "✅ 部署成功！"
    echo "🌐 API地址: https://brapi.smithyan.xyz"
    echo "🌐 Web地址: https://br.smithyan.xyz"
else
    echo "❌ 部署失败，请检查日志"
    docker-compose logs
    exit 1
fi
```

### 第五步：创建零停机更新脚本

创建 `scripts/rolling-update.sh`:

```bash
#!/bin/bash

# 零停机更新脚本
set -e

PROJECT_DIR="/www/wwwroot/burner-repair"
BACKUP_DIR="/www/backup/burner-repair"
DATE=$(date +%Y%m%d_%H%M%S)

echo "🔄 开始零停机更新..."

cd $PROJECT_DIR

# 备份数据
echo "📦 备份数据..."
mkdir -p $BACKUP_DIR
tar -czf "$BACKUP_DIR/data_backup_$DATE.tar.gz" -C "$PROJECT_DIR" data

# 更新API服务
echo "🔄 更新API服务..."
docker-compose up -d --no-deps --build api

# 等待API服务健康
echo "⏳ 等待API服务健康..."
timeout 60 bash -c 'until docker-compose exec api wget --spider -q http://localhost:12140/api/v1/health; do sleep 2; done'

# 更新Web服务
echo "🔄 更新Web服务..."
docker-compose up -d --no-deps --build web

# 等待Web服务健康
echo "⏳ 等待Web服务健康..."
timeout 60 bash -c 'until docker-compose exec web wget --spider -q http://localhost:12141; do sleep 2; done'

echo "✅ 零停机更新完成！"
```

### 第六步：配置Nginx反向代理

#### 6.1 在宝塔面板中添加站点

1. 登录宝塔面板
2. 点击"网站" → "添加站点"
3. 添加两个站点：
   - `brapi.smithyan.xyz` (API域名)
   - `br.smithyan.xyz` (Web域名)

#### 6.2 配置API域名反向代理

在宝塔面板中，编辑 `brapi.smithyan.xyz` 的Nginx配置：

```nginx
server {
    listen 80;
    listen 443 ssl http2;
    server_name brapi.smithyan.xyz;

    # SSL配置（宝塔面板自动生成）
    ssl_certificate /www/server/panel/vhost/cert/brapi.smithyan.xyz/fullchain.pem;
    ssl_certificate_key /www/server/panel/vhost/cert/brapi.smithyan.xyz/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:HIGH:!aNULL:!MD5:!RC4:!DHE;
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # 强制HTTPS
    if ($scheme != "https") {
        return 301 https://$server_name$request_uri;
    }

    # 日志配置
    access_log /www/wwwlogs/brapi.smithyan.xyz.log;
    error_log /www/wwwlogs/brapi.smithyan.xyz.error.log;

    # 客户端配置
    client_max_body_size 100M;
    client_body_timeout 60s;
    client_header_timeout 60s;

    # WebSocket支持
    location / {
        proxy_pass http://127.0.0.1:12140;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;

        # 超时配置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;

        # 缓冲配置
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
        proxy_busy_buffers_size 8k;
    }

    # 健康检查
    location /health {
        proxy_pass http://127.0.0.1:12140/api/v1/health;
        access_log off;
    }

    # 静态文件缓存
    location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
        proxy_pass http://127.0.0.1:12140;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

#### 6.3 配置Web域名反向代理

编辑 `br.smithyan.xyz` 的Nginx配置：

```nginx
server {
    listen 80;
    listen 443 ssl http2;
    server_name br.smithyan.xyz;

    # SSL配置（宝塔面板自动生成）
    ssl_certificate /www/server/panel/vhost/cert/br.smithyan.xyz/fullchain.pem;
    ssl_certificate_key /www/server/panel/vhost/cert/br.smithyan.xyz/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:HIGH:!aNULL:!MD5:!RC4:!DHE;
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # 强制HTTPS
    if ($scheme != "https") {
        return 301 https://$server_name$request_uri;
    }

    # 日志配置
    access_log /www/wwwlogs/br.smithyan.xyz.log;
    error_log /www/wwwlogs/br.smithyan.xyz.error.log;

    # 客户端配置
    client_max_body_size 100M;

    # 主要代理配置
    location / {
        proxy_pass http://127.0.0.1:12141;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;

        # 超时配置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        proxy_pass http://127.0.0.1:12141;
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary Accept-Encoding;
    }

    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
}
```

### 第七步：创建监控脚本

创建 `scripts/monitor.sh`:

```bash
#!/bin/bash

# 系统监控脚本
PROJECT_DIR="/www/wwwroot/burner-repair"
LOG_FILE="/www/wwwlogs/burner-repair-monitor.log"

# 记录日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> $LOG_FILE
}

# 检查服务状态
check_services() {
    cd $PROJECT_DIR

    # 检查Docker容器状态
    if ! docker-compose ps | grep -q "Up"; then
        log "ERROR: 发现服务异常，尝试重启..."
        docker-compose restart
        sleep 30
    fi

    # 检查API健康状态
    if ! curl -f -s http://localhost:12140/api/v1/health > /dev/null; then
        log "ERROR: API服务健康检查失败"
        docker-compose restart api
    fi

    # 检查Web服务状态
    if ! curl -f -s http://localhost:12141 > /dev/null; then
        log "ERROR: Web服务健康检查失败"
        docker-compose restart web
    fi

    # 检查磁盘空间
    DISK_USAGE=$(df /www | tail -1 | awk '{print $5}' | sed 's/%//')
    if [ $DISK_USAGE -gt 80 ]; then
        log "WARNING: 磁盘使用率超过80%: ${DISK_USAGE}%"
    fi

    # 检查内存使用
    MEM_USAGE=$(free | grep Mem | awk '{printf "%.0f", $3/$2 * 100.0}')
    if [ $MEM_USAGE -gt 85 ]; then
        log "WARNING: 内存使用率超过85%: ${MEM_USAGE}%"
    fi

    log "INFO: 系统监控检查完成"
}

# 执行监控
check_services
```

### 第八步：创建日志轮转配置

创建 `config/logrotate.conf`:

```bash
/www/wwwroot/burner-repair/logs/*/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www www
    postrotate
        docker-compose -f /www/wwwroot/burner-repair/docker-compose.yml restart api web
    endscript
}

/www/wwwlogs/burner-repair*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www www
}
```

## 🛠️ 部署操作步骤

### 步骤1：上传代码到服务器

```bash
# 方法1：使用Git（推荐）
cd /www/wwwroot
git clone https://github.com/your-repo/burner-repair.git
cd burner-repair

# 方法2：使用宝塔面板文件管理器上传压缩包
# 上传后解压到 /www/wwwroot/burner-repair
```

### 步骤2：设置权限和环境

```bash
# 设置目录权限
chmod +x /www/wwwroot/burner-repair/scripts/*.sh

# 创建必要目录
mkdir -p /www/wwwroot/burner-repair/{data/mongodb,data/uploads,logs/{api,web,mongodb},config}

# 设置所有者
chown -R www:www /www/wwwroot/burner-repair
```

### 步骤3：配置环境文件

创建 `api/.env.production`:

```env
NODE_ENV=production
PORT=12140
MONGODB_URI=mongodb://admin:BurnerRepair2024!@#@mongodb:27017/burner_repair?authSource=admin
JWT_SECRET=BurnerRepairJWT2024SecretKey!@#$%^&*
JWT_REFRESH_SECRET=BurnerRepairRefreshJWT2024SecretKey!@#$%^&*
UPLOAD_PATH=/app/uploads
LOG_LEVEL=info
CORS_ORIGIN=https://br.smithyan.xyz
```

创建 `web/.env.production`:

```env
NODE_ENV=production
VITE_API_BASE_URL=https://brapi.smithyan.xyz
VITE_WS_URL=wss://brapi.smithyan.xyz
```

### 步骤4：执行部署

```bash
# 进入项目目录
cd /www/wwwroot/burner-repair

# 执行部署脚本
./scripts/deploy.sh
```

### 步骤5：配置SSL证书

1. 在宝塔面板中，进入"网站"页面
2. 找到 `brapi.smithyan.xyz`，点击"设置"
3. 选择"SSL"选项卡，点击"Let's Encrypt"
4. 勾选域名，点击"申请"
5. 重复步骤2-4为 `br.smithyan.xyz` 申请证书

### 步骤6：设置定时任务

在宝塔面板中添加以下定时任务：

```bash
# 系统监控（每5分钟执行）
*/5 * * * * /www/wwwroot/burner-repair/scripts/monitor.sh

# 日志清理（每天凌晨2点执行）
0 2 * * * /usr/sbin/logrotate -f /www/wwwroot/burner-repair/config/logrotate.conf

# 数据库备份（每天凌晨3点执行）
0 3 * * * docker exec burner-repair-mongodb mongodump --authenticationDatabase admin -u admin -p 'BurnerRepair2024!@#' --out /data/db/backup/$(date +\%Y\%m\%d)
```

## 🔧 运维管理

### 查看服务状态

```bash
cd /www/wwwroot/burner-repair

# 查看所有服务状态
docker-compose ps

# 查看服务日志
docker-compose logs -f api
docker-compose logs -f web
docker-compose logs -f mongodb

# 查看系统资源使用
docker stats
```

### 服务管理命令

```bash
# 启动所有服务
docker-compose up -d

# 停止所有服务
docker-compose down

# 重启特定服务
docker-compose restart api
docker-compose restart web

# 查看服务健康状态
curl https://brapi.smithyan.xyz/api/v1/health
curl https://br.smithyan.xyz
```

### 数据备份与恢复

```bash
# 手动备份数据库
docker exec burner-repair-mongodb mongodump \
  --authenticationDatabase admin \
  -u admin -p 'BurnerRepair2024!@#' \
  --out /data/db/backup/manual_$(date +%Y%m%d_%H%M%S)

# 恢复数据库
docker exec burner-repair-mongodb mongorestore \
  --authenticationDatabase admin \
  -u admin -p 'BurnerRepair2024!@#' \
  /data/db/backup/20241201_120000

# 备份上传文件
tar -czf /www/backup/uploads_$(date +%Y%m%d_%H%M%S).tar.gz \
  -C /www/wwwroot/burner-repair data/uploads
```

### 更新部署

```bash
# 零停机更新
cd /www/wwwroot/burner-repair
./scripts/rolling-update.sh

# 或者手动更新
git pull origin main
docker-compose build --no-cache
docker-compose up -d --force-recreate
```

## 🚨 故障排查

### 常见问题及解决方案

#### 1. 服务无法启动

```bash
# 检查端口占用
netstat -tlnp | grep -E '12139|12140|12141'

# 检查Docker日志
docker-compose logs api
docker-compose logs web
docker-compose logs mongodb

# 检查磁盘空间
df -h

# 检查内存使用
free -h
```

#### 2. 数据库连接失败

```bash
# 检查MongoDB容器状态
docker exec burner-repair-mongodb mongosh \
  --authenticationDatabase admin \
  -u admin -p 'BurnerRepair2024!@#' \
  --eval "db.adminCommand('ping')"

# 检查网络连接
docker network ls
docker network inspect burner-repair_burner-repair-network
```

#### 3. SSL证书问题

```bash
# 检查证书有效期
openssl x509 -in /www/server/panel/vhost/cert/brapi.smithyan.xyz/fullchain.pem -text -noout | grep "Not After"

# 重新申请证书（在宝塔面板中操作）
# 或者手动续期
certbot renew --nginx
```

#### 4. 性能问题

```bash
# 查看系统负载
top
htop

# 查看Docker容器资源使用
docker stats

# 查看Nginx访问日志
tail -f /www/wwwlogs/brapi.smithyan.xyz.log
tail -f /www/wwwlogs/br.smithyan.xyz.log
```

## 📊 监控指标

### 关键监控指标

1. **服务可用性**
   - API健康检查: `https://brapi.smithyan.xyz/api/v1/health`
   - Web服务状态: `https://br.smithyan.xyz`

2. **系统资源**
   - CPU使用率 < 80%
   - 内存使用率 < 85%
   - 磁盘使用率 < 80%

3. **应用指标**
   - 响应时间 < 2秒
   - 错误率 < 1%
   - 并发连接数

4. **数据库指标**
   - 连接数
   - 查询响应时间
   - 存储空间使用

### 告警配置

建议配置以下告警：

- 服务不可用超过5分钟
- CPU使用率超过80%持续10分钟
- 内存使用率超过85%持续5分钟
- 磁盘使用率超过80%
- 错误率超过5%持续5分钟

## 🔐 安全配置

### 防火墙设置

```bash
# 只开放必要端口
ufw allow 22    # SSH
ufw allow 80    # HTTP
ufw allow 443   # HTTPS
ufw allow 8888  # 宝塔面板

# 禁止直接访问应用端口
ufw deny 12139  # MongoDB
ufw deny 12140  # API
ufw deny 12141  # Web
```

### 定期安全检查

```bash
# 检查异常登录
last | head -20

# 检查系统更新
apt update && apt list --upgradable

# 检查Docker镜像安全
docker scan burner-repair-api
docker scan burner-repair-web
```

## 📝 部署检查清单

- [ ] 服务器环境准备完成
- [ ] 域名DNS解析配置正确
- [ ] 代码上传到服务器
- [ ] Docker Compose配置文件创建
- [ ] 环境变量配置完成
- [ ] 数据目录权限设置正确
- [ ] Nginx反向代理配置完成
- [ ] SSL证书申请并配置
- [ ] 服务启动成功
- [ ] 健康检查通过
- [ ] 监控脚本配置
- [ ] 定时任务设置
- [ ] 备份策略配置
- [ ] 防火墙规则设置
- [ ] 性能测试完成

## 🎯 部署完成验证

部署完成后，请验证以下功能：

1. **访问测试**
   - 访问 https://br.smithyan.xyz 确认Web应用正常
   - 访问 https://brapi.smithyan.xyz/api/v1/health 确认API正常

2. **功能测试**
   - 用户注册和登录
   - 创建维修记录
   - 文件上传功能
   - 实时通知功能

3. **性能测试**
   - 页面加载速度
   - API响应时间
   - 并发用户测试

4. **安全测试**
   - HTTPS强制跳转
   - API认证机制
   - 文件上传安全

恭喜！燃烧器维修系统已成功部署到生产环境！🎉
