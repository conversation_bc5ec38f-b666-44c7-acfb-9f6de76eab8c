# 燃烧器维修管理系统

## 📋 项目概述

燃烧器维修管理系统是一个现代化的企业级维修工作流管理平台，支持多平台协作，包括Web管理端、移动端应用和完整的后端API服务。

## 🏗️ 项目架构

```
燃烧器维修管理系统
├── 后端API服务 (Node.js + Express + TypeScript)
├── Web管理端 (React + TypeScript + Ant Design Pro)
└── 移动端应用 (uni-appx框架，开发中)
```

## 📁 项目结构

```
burner-repair-ag/
├── api/                          # 后端API服务
│   ├── src/                      # 源代码
│   ├── tests/                    # 测试文件
│   ├── docs/                     # API文档
│   ├── config/                   # 配置文件
│   ├── nginx.conf                # Nginx配置
│   ├── Dockerfile                # Docker构建文件
│   └── package.json              # 依赖配置
├── web/                          # Web前端应用
│   ├── src/                      # 源代码
│   ├── public/                   # 静态资源
│   ├── nginx.conf                # Nginx配置
│   ├── test-frontend-users.js    # 前端测试脚本
│   └── package.json              # 依赖配置
├── program-word/                 # 项目文档
│   ├── 01-产品设计.md            # 产品设计文档
│   ├── 02-技术设计.md            # 技术架构设计
│   ├── 03-开发指南.md            # 开发指南
│   ├── 04-部署指南.md            # 部署指南
│   └── 05-应用发布.md            # 应用发布指南
├── docker-compose.prod.yml       # 生产环境Docker配置
├── deploy-production.sh          # 生产环境部署脚本
├── quick-deploy.sh               # 快速部署脚本
├── 生产环境部署指南.md           # 详细部署指南
├── 宝塔面板配置指南.md           # 宝塔面板配置说明
└── README.md                     # 项目说明文档
```

## 🚀 快速开始

### 开发环境要求

- **Node.js**: 18.0+
- **MongoDB**: 5.0+
- **Redis**: 6.0+ (可选)
- **Docker**: 20.10+ (生产环境)

### 本地开发

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd burner-repair-ag
   ```

2. **启动后端API**
   ```bash
   cd api
   npm install
   npm run dev
   ```

3. **启动Web前端**
   ```bash
   cd web
   npm install
   npm run dev
   ```

4. **访问应用**
   - API服务: http://localhost:3000
   - Web应用: http://localhost:5173

### 生产环境部署

#### 方式一：快速部署
```bash
chmod +x quick-deploy.sh
./quick-deploy.sh
```

#### 方式二：完整部署
```bash
chmod +x deploy-production.sh
./deploy-production.sh
```

详细部署说明请参考：
- [生产环境部署指南.md](./生产环境部署指南.md)
- [宝塔面板配置指南.md](./宝塔面板配置指南.md)

## 🔧 技术栈

### 后端技术
- **框架**: Node.js + Express + TypeScript
- **数据库**: MongoDB + Mongoose ODM
- **认证**: JWT + 权限控制
- **实时通信**: Socket.io
- **文件处理**: Multer + Sharp
- **API文档**: Swagger/OpenAPI
- **测试**: Jest + Supertest
- **日志**: Winston
- **安全**: Helmet + CORS + 速率限制

### 前端技术
- **框架**: React 18 + TypeScript
- **UI组件**: Ant Design Pro
- **状态管理**: Redux Toolkit
- **构建工具**: Vite
- **路由**: React Router
- **HTTP客户端**: Axios
- **实时通信**: Socket.io Client
- **测试**: Vitest + Testing Library

### 部署技术
- **容器化**: Docker + Docker Compose
- **反向代理**: Nginx
- **SSL证书**: Let's Encrypt
- **监控**: 自定义监控脚本
- **备份**: 自动化数据库备份

## 📊 功能特性

### 核心功能
- ✅ 用户认证和权限管理
- ✅ 站点管理系统
- ✅ 维修记录管理
- ✅ 文件上传和管理
- ✅ 实时通知系统
- ✅ 数据导出功能
- ✅ 移动端支持

### 管理功能
- ✅ 用户角色管理
- ✅ 权限控制系统
- ✅ 系统配置管理
- ✅ 日志审计功能
- ✅ 数据统计分析

### 技术特性
- ✅ RESTful API设计
- ✅ 响应式Web界面
- ✅ 实时数据同步
- ✅ 文件安全上传
- ✅ 数据库事务支持
- ✅ 缓存优化
- ✅ 安全防护

## 🌐 访问地址

### 生产环境
- **Web管理端**: https://br.smithyan.xyz
- **API服务**: https://brapi.smithyan.xyz
- **API文档**: https://brapi.smithyan.xyz/api-docs

### 开发环境
- **Web应用**: http://localhost:5173
- **API服务**: http://localhost:3000
- **API文档**: http://localhost:3000/api-docs

## 📖 文档说明

- **[产品设计](./program-word/01-产品设计.md)**: 产品需求和功能设计
- **[技术设计](./program-word/02-技术设计.md)**: 系统架构和技术选型
- **[开发指南](./program-word/03-开发指南.md)**: 开发环境搭建和编码规范
- **[部署指南](./program-word/04-部署指南.md)**: 部署流程和配置说明
- **[应用发布](./program-word/05-应用发布.md)**: 发布流程和版本管理

## 🔒 安全特性

- JWT令牌认证
- 角色权限控制
- API速率限制
- CORS跨域保护
- SQL注入防护
- XSS攻击防护
- 文件上传安全检查
- HTTPS强制加密

## 📈 性能优化

- Redis缓存支持
- 数据库索引优化
- 静态资源CDN
- Gzip压缩
- 图片压缩处理
- 懒加载优化
- 分页查询优化

## 🛠️ 开发工具

- **代码编辑器**: VS Code (推荐)
- **API测试**: Postman / Insomnia
- **数据库管理**: MongoDB Compass
- **版本控制**: Git
- **包管理**: npm / yarn
- **容器管理**: Docker Desktop

## 📞 技术支持

如遇到问题，请检查：

1. **开发环境问题**: 查看 [开发指南](./program-word/03-开发指南.md)
2. **部署问题**: 查看 [部署指南](./program-word/04-部署指南.md)
3. **配置问题**: 查看 [宝塔面板配置指南](./宝塔面板配置指南.md)
4. **API问题**: 访问 API文档查看接口说明

## 📄 许可证

本项目采用 MIT 许可证。详情请参阅 LICENSE 文件。

---

**燃烧器维修管理系统** - 现代化的企业级维修工作流管理平台 🔧
