# 燃烧器维修系统 - 生产环境部署指南

## 📋 部署概述

本指南详细说明如何在生产环境中部署燃烧器维修管理系统，包括服务器配置、域名设置、SSL证书配置等完整流程。

## 🏗️ 系统架构

```
用户访问
    ↓
Nginx反向代理 (80/443)
    ↓
┌─────────────────┬─────────────────┐
│   Web前端       │    API后端      │
│ (端口: 12141)   │  (端口: 12140)  │
└─────────────────┴─────────────────┘
    ↓
MongoDB数据库 (端口: 12139)
```

## 🔧 环境要求

### 服务器配置
- **操作系统**: CentOS 7+ / Ubuntu 18.04+
- **内存**: 最低 2GB，推荐 4GB+
- **存储**: 最低 20GB，推荐 50GB+
- **CPU**: 最低 2核，推荐 4核+

### 软件依赖
- Docker 20.10+
- Docker Compose 1.29+
- Nginx 1.18+
- 宝塔面板 (可选，推荐)

## 🚀 快速部署

### 方式一：使用快速部署脚本

1. **上传项目文件**
   ```bash
   # 将项目文件上传到服务器
   scp -r burner-repair-ag/ root@your-server:/tmp/
   ```

2. **执行快速部署**
   ```bash
   cd /tmp/burner-repair-ag
   chmod +x quick-deploy.sh
   ./quick-deploy.sh
   ```

### 方式二：使用生产环境部署脚本

1. **执行完整部署**
   ```bash
   cd /tmp/burner-repair-ag
   chmod +x deploy-production.sh
   ./deploy-production.sh
   ```

## 🌐 域名和SSL配置

### 1. 域名解析设置

在域名服务商处添加以下DNS记录：

```
类型    主机记录    记录值
A       br         your-server-ip
A       brapi      your-server-ip
```

### 2. 宝塔面板配置

#### 添加站点
1. 登录宝塔面板
2. 点击"网站" → "添加站点"
3. 添加以下两个站点：
   - `br.smithyan.xyz` (Web前端)
   - `brapi.smithyan.xyz` (API后端)

#### 申请SSL证书
1. 在站点管理中点击"SSL"
2. 选择"Let's Encrypt"免费证书
3. 勾选域名并申请证书
4. 开启"强制HTTPS"

### 3. Nginx配置

#### API域名配置 (brapi.smithyan.xyz)
```bash
# 复制API Nginx配置
cp /www/wwwroot/burner-repair/api/nginx.conf /www/server/panel/vhost/nginx/brapi.smithyan.xyz.conf

# 重载Nginx配置
nginx -t && nginx -s reload
```

#### Web域名配置 (br.smithyan.xyz)
```bash
# 复制Web Nginx配置
cp /www/wwwroot/burner-repair/web/nginx.conf /www/server/panel/vhost/nginx/br.smithyan.xyz.conf

# 重载Nginx配置
nginx -t && nginx -s reload
```

## 🔍 服务验证

### 1. 检查Docker容器状态
```bash
cd /www/wwwroot/burner-repair
docker-compose ps
```

预期输出：
```
Name                    Command               State           Ports
------------------------------------------------------------------------
burner-repair-api       sh -c npm install --prod ...   Up      0.0.0.0:12140->12140/tcp
burner-repair-mongodb   docker-entrypoint.sh mongod    Up      0.0.0.0:12139->27017/tcp
burner-repair-web       sh -c npm install && npm ...   Up      0.0.0.0:12141->12141/tcp
```

### 2. 健康检查
```bash
# 检查API服务
curl -f http://localhost:12140/api/v1/health

# 检查Web服务
curl -f http://localhost:12141

# 检查MongoDB
docker exec burner-repair-mongodb mongosh --eval "db.adminCommand('ping')"
```

### 3. 访问测试
- API文档: https://brapi.smithyan.xyz/api-docs
- Web管理端: https://br.smithyan.xyz
- API健康检查: https://brapi.smithyan.xyz/api/v1/health

## 📊 监控和维护

### 1. 设置监控脚本
```bash
# 添加定时任务
crontab -e

# 每5分钟执行一次监控检查
*/5 * * * * /www/wwwroot/burner-repair/scripts/monitor.sh
```

### 2. 日志管理
```bash
# 查看API日志
docker-compose logs -f api

# 查看Web日志
docker-compose logs -f web

# 查看MongoDB日志
docker-compose logs -f mongodb
```

### 3. 数据备份
```bash
# 创建备份脚本
cat > /www/backup/backup-burner-repair.sh << 'EOF'
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/www/backup/burner-repair"
PROJECT_DIR="/www/wwwroot/burner-repair"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
docker exec burner-repair-mongodb mongodump --authenticationDatabase admin -u admin -p 'BurnerRepair2024!@#' --out /tmp/backup
docker cp burner-repair-mongodb:/tmp/backup $BACKUP_DIR/mongodb_$DATE

# 备份上传文件
tar -czf $BACKUP_DIR/uploads_$DATE.tar.gz -C $PROJECT_DIR/data uploads

# 清理7天前的备份
find $BACKUP_DIR -name "*" -mtime +7 -delete

echo "备份完成: $DATE"
EOF

chmod +x /www/backup/backup-burner-repair.sh

# 设置每日备份
echo "0 2 * * * /www/backup/backup-burner-repair.sh" | crontab -
```

## 🔧 常用管理命令

### Docker服务管理
```bash
cd /www/wwwroot/burner-repair

# 查看服务状态
docker-compose ps

# 启动服务
docker-compose up -d

# 停止服务
docker-compose down

# 重启服务
docker-compose restart

# 重新构建并启动
docker-compose up -d --build

# 查看实时日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f api
docker-compose logs -f web
docker-compose logs -f mongodb
```

### 系统维护
```bash
# 清理Docker资源
docker system prune -f

# 更新系统
yum update -y  # CentOS
apt update && apt upgrade -y  # Ubuntu

# 检查磁盘空间
df -h

# 检查内存使用
free -h

# 检查系统负载
top
```

## 🚨 故障排除

### 常见问题

#### 1. 服务无法启动
```bash
# 检查端口占用
netstat -tlnp | grep -E "(12139|12140|12141)"

# 检查Docker日志
docker-compose logs

# 重启Docker服务
systemctl restart docker
```

#### 2. 数据库连接失败
```bash
# 检查MongoDB容器状态
docker exec -it burner-repair-mongodb mongosh

# 重置数据库密码
docker exec -it burner-repair-mongodb mongosh admin --eval "
db.createUser({
  user: 'admin',
  pwd: 'BurnerRepair2024!@#',
  roles: ['root']
})
"
```

#### 3. Nginx配置错误
```bash
# 测试Nginx配置
nginx -t

# 查看Nginx错误日志
tail -f /var/log/nginx/error.log

# 重载Nginx配置
nginx -s reload
```

#### 4. SSL证书问题
```bash
# 检查证书有效期
openssl x509 -in /www/server/panel/vhost/cert/br.smithyan.xyz/fullchain.pem -text -noout

# 重新申请证书
# 在宝塔面板中重新申请Let's Encrypt证书
```

## 📞 技术支持

如遇到部署问题，请检查：

1. **系统日志**: `/www/wwwlogs/`
2. **应用日志**: `/www/wwwroot/burner-repair/logs/`
3. **Docker日志**: `docker-compose logs`
4. **Nginx日志**: `/var/log/nginx/`

---

**部署完成后，请访问 https://br.smithyan.xyz 验证系统是否正常运行。**
