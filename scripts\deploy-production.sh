#!/bin/bash

# 生产环境部署脚本
set -e

# 配置变量
PROJECT_DIR="/www/wwwroot/burner-repair"
BACKUP_DIR="/www/backup/burner-repair"
DATE=$(date +%Y%m%d_%H%M%S)
LOG_FILE="/www/wwwlogs/deploy-$DATE.log"

# 日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a $LOG_FILE
}

# 错误处理
error_exit() {
    log "ERROR: $1"
    exit 1
}

log "🚀 开始生产环境部署..."

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    error_exit "请使用root用户执行此脚本"
fi

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    error_exit "Docker未安装，请先安装Docker"
fi

if ! command -v docker-compose &> /dev/null; then
    error_exit "Docker Compose未安装，请先安装Docker Compose"
fi

# 创建项目目录
log "📁 创建项目目录结构..."
mkdir -p $PROJECT_DIR
mkdir -p $BACKUP_DIR
mkdir -p $PROJECT_DIR/{data/mongodb,data/uploads,logs/{api,web,mongodb},config,scripts,ssl}

# 设置权限
chown -R www:www $PROJECT_DIR
chmod -R 755 $PROJECT_DIR

# 备份现有数据（如果存在）
if [ -d "$PROJECT_DIR/data" ] && [ "$(ls -A $PROJECT_DIR/data)" ]; then
    log "📦 备份现有数据..."
    tar -czf "$BACKUP_DIR/data_backup_$DATE.tar.gz" -C "$PROJECT_DIR" data
fi

# 停止现有服务
log "⏹️ 停止现有服务..."
cd $PROJECT_DIR
if [ -f "docker-compose.yml" ]; then
    docker-compose down --remove-orphans || true
fi

# 创建MongoDB配置文件
log "⚙️ 创建MongoDB配置文件..."
cat > $PROJECT_DIR/config/mongod.conf << 'EOF'
# MongoDB配置文件
storage:
  dbPath: /data/db
  journal:
    enabled: true

systemLog:
  destination: file
  logAppend: true
  path: /var/log/mongodb/mongod.log
  logRotate: reopen

net:
  port: 27017
  bindIp: 0.0.0.0

processManagement:
  timeZoneInfo: /usr/share/zoneinfo

security:
  authorization: enabled

setParameter:
  authenticationMechanisms: SCRAM-SHA-1,SCRAM-SHA-256
EOF

# 创建环境配置文件
log "🔧 创建环境配置文件..."

# API环境配置
mkdir -p $PROJECT_DIR/api
cat > $PROJECT_DIR/api/.env.production << 'EOF'
NODE_ENV=production
PORT=12140
MONGODB_URI=mongodb://admin:BurnerRepair2024!@#@mongodb:27017/burner_repair?authSource=admin
JWT_SECRET=BurnerRepairJWT2024SecretKey!@#$%^&*
JWT_REFRESH_SECRET=BurnerRepairRefreshJWT2024SecretKey!@#$%^&*
UPLOAD_PATH=/app/uploads
LOG_LEVEL=info
CORS_ORIGIN=https://br.smithyan.xyz
EOF

# Web环境配置
mkdir -p $PROJECT_DIR/web
cat > $PROJECT_DIR/web/.env.production << 'EOF'
NODE_ENV=production
VITE_API_BASE_URL=https://brapi.smithyan.xyz
VITE_WS_URL=wss://brapi.smithyan.xyz
EOF

# 复制Docker Compose配置
log "📋 创建Docker Compose配置..."
cp docker-compose.prod.yml $PROJECT_DIR/docker-compose.yml

# 拉取最新镜像
log "📥 拉取最新镜像..."
docker-compose pull

# 构建并启动服务
log "🔨 构建并启动服务..."
docker-compose up -d --build

# 等待服务启动
log "⏳ 等待服务启动..."
sleep 60

# 健康检查
log "🔍 执行健康检查..."
RETRY_COUNT=0
MAX_RETRIES=10

# 检查MongoDB
while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
    if docker exec burner-repair-mongodb mongosh --eval "db.adminCommand('ping')" > /dev/null 2>&1; then
        log "✅ MongoDB健康检查通过"
        break
    fi
    RETRY_COUNT=$((RETRY_COUNT + 1))
    log "⏳ MongoDB健康检查失败，重试 $RETRY_COUNT/$MAX_RETRIES..."
    sleep 10
done

if [ $RETRY_COUNT -eq $MAX_RETRIES ]; then
    error_exit "MongoDB健康检查失败"
fi

# 检查API服务
RETRY_COUNT=0
while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
    if curl -f -s http://localhost:12140/api/v1/health > /dev/null; then
        log "✅ API服务健康检查通过"
        break
    fi
    RETRY_COUNT=$((RETRY_COUNT + 1))
    log "⏳ API服务健康检查失败，重试 $RETRY_COUNT/$MAX_RETRIES..."
    sleep 10
done

if [ $RETRY_COUNT -eq $MAX_RETRIES ]; then
    error_exit "API服务健康检查失败"
fi

# 检查Web服务
RETRY_COUNT=0
while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
    if curl -f -s http://localhost:12141 > /dev/null; then
        log "✅ Web服务健康检查通过"
        break
    fi
    RETRY_COUNT=$((RETRY_COUNT + 1))
    log "⏳ Web服务健康检查失败，重试 $RETRY_COUNT/$MAX_RETRIES..."
    sleep 10
done

if [ $RETRY_COUNT -eq $MAX_RETRIES ]; then
    error_exit "Web服务健康检查失败"
fi

# 显示服务状态
log "📊 服务状态："
docker-compose ps

# 创建监控脚本
log "📊 创建监控脚本..."
cat > $PROJECT_DIR/scripts/monitor.sh << 'EOF'
#!/bin/bash

# 系统监控脚本
PROJECT_DIR="/www/wwwroot/burner-repair"
LOG_FILE="/www/wwwlogs/burner-repair-monitor.log"

# 记录日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> $LOG_FILE
}

# 检查服务状态
check_services() {
    cd $PROJECT_DIR
    
    # 检查Docker容器状态
    if ! docker-compose ps | grep -q "Up"; then
        log "ERROR: 发现服务异常，尝试重启..."
        docker-compose restart
        sleep 30
    fi
    
    # 检查API健康状态
    if ! curl -f -s http://localhost:12140/api/v1/health > /dev/null; then
        log "ERROR: API服务健康检查失败"
        docker-compose restart api
    fi
    
    # 检查Web服务状态
    if ! curl -f -s http://localhost:12141 > /dev/null; then
        log "ERROR: Web服务健康检查失败"
        docker-compose restart web
    fi
    
    # 检查磁盘空间
    DISK_USAGE=$(df /www | tail -1 | awk '{print $5}' | sed 's/%//')
    if [ $DISK_USAGE -gt 80 ]; then
        log "WARNING: 磁盘使用率超过80%: ${DISK_USAGE}%"
    fi
    
    # 检查内存使用
    MEM_USAGE=$(free | grep Mem | awk '{printf "%.0f", $3/$2 * 100.0}')
    if [ $MEM_USAGE -gt 85 ]; then
        log "WARNING: 内存使用率超过85%: ${MEM_USAGE}%"
    fi
    
    log "INFO: 系统监控检查完成"
}

# 执行监控
check_services
EOF

chmod +x $PROJECT_DIR/scripts/monitor.sh

# 创建日志轮转配置
log "📝 创建日志轮转配置..."
cat > $PROJECT_DIR/config/logrotate.conf << 'EOF'
/www/wwwroot/burner-repair/logs/*/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www www
    postrotate
        docker-compose -f /www/wwwroot/burner-repair/docker-compose.yml restart api web
    endscript
}

/www/wwwlogs/burner-repair*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www www
}
EOF

log "✅ 部署成功完成！"
log "🌐 API地址: https://brapi.smithyan.xyz"
log "🌐 Web地址: https://br.smithyan.xyz"
log "📋 请按照部署文档配置Nginx反向代理和SSL证书"
log "📊 监控脚本已创建: $PROJECT_DIR/scripts/monitor.sh"
log "📝 部署日志: $LOG_FILE"

echo ""
echo "🎉 燃烧器维修系统部署完成！"
echo "📋 下一步操作："
echo "1. 在宝塔面板中配置域名和SSL证书"
echo "2. 配置Nginx反向代理"
echo "3. 设置定时任务执行监控脚本"
echo "4. 访问 https://br.smithyan.xyz 验证部署"
